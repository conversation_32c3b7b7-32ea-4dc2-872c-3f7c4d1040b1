apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
patches:
- path: ./deployment-environment-variables.yaml
- path: ./deployment-volumes.yaml
- path: ./deployment-server-port.yaml
- path: ./deployment-sa.yaml
- path: ./deployment-hpa.yaml
- patch: |-
    - op: replace
      path: "/apiVersion"
      value: autoscaling/v2
  target:
    group: autoscaling
    version: v2beta2
images:
- name: 557068259488.dkr.ecr.eu-west-2.amazonaws.com/protobuf-deserializer
  newTag: 909ea3581b84b65c9ebd8ff40c890219d441e88c
resources:
- ../base
