apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: protobuf-deserializer-deployment
  namespace: vehicle-lifecycle
  labels:
    app: protobuf-deserializer
    app.kubernetes.io/instance: protobuf-deserializer
    tags.datadoghq.com/env: cn-dev
    tags.datadoghq.com/service: protobuf-deserializer
    tags.datadoghq.com/version: 2025-06-19-0753
spec:
  serviceName: protobuf-deserializer
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: "cn-dev"
        tags.datadoghq.com/service: "protobuf-deserializer"
        tags.datadoghq.com/version: 2025-06-19-0753
    spec:
      containers:
        - name: protobuf-deserializer
          env:
            - name: spring.profiles.active
              value: cn-dev
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: DD_ENV
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['tags.datadoghq.com/env']
            - name: DD_SERVICE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['tags.datadoghq.com/service']
            - name: DD_VERSION
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['tags.datadoghq.com/version']
            - name: DD_PROFILING_ENABLED
              value: "false"
            - name: DD_TRACE_ENABLED
              value: "false"
            - name: DD_LOGS_INJECTION
              value: "true"
            - name: HIVEMQ_PORT
              value: "443"
  selector:
    matchLabels:
      app: protobuf-deserializer
