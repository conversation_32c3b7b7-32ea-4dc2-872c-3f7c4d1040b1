apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
patches:
- path: ./deployment-environment-variables.yaml
- path: ./deployment-server-port.yaml
- path: ./deployment-volumes.yaml
- path: ./deployment-sa.yaml
- path: ./deployment-hpa.yaml
- patch: |-
    - op: replace
      path: "/apiVersion"
      value: autoscaling/v2
  target:
    group: autoscaling
    version: v2beta2

images:
- name: 557068259488.dkr.ecr.eu-west-2.amazonaws.com/protobuf-deserializer
  newTag: f0b59ccfc34850bd56b2d68eeba68a26f04a1488
resources:
- ../base
