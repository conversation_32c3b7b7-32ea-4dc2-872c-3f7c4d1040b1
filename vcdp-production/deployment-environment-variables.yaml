apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: protobuf-deserializer-deployment
  namespace: vehicle-lifecycle
  labels:
    app: protobuf-deserializer
    app.kubernetes.io/instance: protobuf-deserializer
    tags.datadoghq.com/env: vcdp-production
    tags.datadoghq.com/service: protobuf-deserializer
    tags.datadoghq.com/version: 2025-01-16-0910
spec:
  serviceName: protobuf-deserializer
  template:
    metadata:
      labels:
        tags.datadoghq.com/env: "vcdp-production"
        tags.datadoghq.com/service: "protobuf-deserializer"
        tags.datadoghq.com/version: 2025-01-16-0910
    spec:
      containers:
        - name: protobuf-deserializer
          env:
            - name: spring.profiles.active
              value: prod
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: DD_ENV
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['tags.datadoghq.com/env']
            - name: DD_SERVICE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['tags.datadoghq.com/service']
            - name: DD_VERSION
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['tags.datadoghq.com/version']
            - name: DD_PROFILING_ENABLED
              value: "true"
            - name: DD_LOGS_INJECTION
              value: "true"
            - name: DD_TRACE_SAMPLE_RATE
              value: "1"
            - name: DD_DOGSTATD_NON_LOCAL_TRAFFIC
              value: "true"
            - name: DD_DATA_STREAMS_ENABLED
              value: "true"
            - name: MQTT_CLIENT_KEY_SECRET
              value: "arn:aws:secretsmanager:eu-west-2:383962037718:secret:certificate/protobuf_deserializer_to_mqtt_client_prod_2_2025-03-11.key"
            - name: MQTT_CLIENT_PASSPHRASE_SECRET
              value: "arn:aws:secretsmanager:eu-west-2:383962037718:secret:certificate/protobuf_deserializer_to_mqtt_client_prod_2_2025-03-11_key.passphrase"
            - name: MQTT_CLIENT_CERT_SECRET
              value: "arn:aws:secretsmanager:eu-west-2:383962037718:secret:certificate/protobuf_deserializer_to_mqtt_client_prod_2_2025-03-11_chain.pem"
            - name: IS_UPDATED_HIVE_MQ_CERT
              value: "true"
  selector:
    matchLabels:
      app: protobuf-deserializer
