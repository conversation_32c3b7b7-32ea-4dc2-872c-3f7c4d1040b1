apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: protobuf-deserializer-deployment
  namespace: vehicle-lifecycle
  labels:
    app: protobuf-deserializer
    app.kubernetes.io/instance: protobuf-deserializer
spec:
  template:
    spec:
      containers:
        - name: protobuf-deserializer
          env:
            - name: SERVER_PORT
              value: "8080"
          ports:
            - containerPort: 8080
              name: http

