apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: protobuf-deserializer-deployment
  namespace: vehicle-lifecycle
  labels:
    app: protobuf-deserializer
spec:
  serviceName: protobuf-deserializer
  selector:
    matchLabels:
      app: protobuf-deserializer
  template:
    metadata:
      labels:
        app: protobuf-deserializer
        version: 2022-11-30-1044
    spec:
      serviceAccountName: protobuf-deserializer
      automountServiceAccountToken: true
      dnsConfig:
        nameservers:
          - 8.8.8.8
        options:
          - name: single-request-reopen
          - name: use-vc
      securityContext:
        runAsUser: 1000
        runAsGroup: 3000
        fsGroup: 2000
      containers:
        - name: protobuf-deserializer
          image: ************.dkr.ecr.eu-west-2.amazonaws.com/protobuf-deserializer:latest
          env:
            - name: SERVER_PORT
              value: "8080"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          ports:
            - containerPort: 8080
              name: http
          livenessProbe:
            initialDelaySeconds: 90
            periodSeconds: 60
            timeoutSeconds: 30
            failureThreshold: 3
            successThreshold: 1
            httpGet:
              path: /actuator/health/liveness
              port: 8080
          readinessProbe:
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 1
            failureThreshold: 3
            successThreshold: 1
            httpGet:
              path: /actuator/health/readiness
              port: 8080
          resources:
            limits:
              cpu: 1
              memory: 1Gi
            requests:
              cpu: 250m
              memory: 256Mi
