apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
patches:
- path: ./deployment-environment-variables.yaml
- path: ./deployment-volumes.yaml
- path: ./deployment-server-port.yaml
- path: ./deployment-sa.yaml
- path: ./deployment-hpa.yaml
- patch: |-
    - op: remove
      path: "/spec/template/spec/dnsConfig/nameservers"
  target:
    kind: StatefulSet
- patch: |-
    - op: replace
      path: "/apiVersion"
      value: autoscaling/v2
  target:
    group: autoscaling
    version: v2beta2
images:
- name: 557068259488.dkr.ecr.eu-west-2.amazonaws.com/protobuf-deserializer
  newName: 068946021388.dkr.ecr.cn-northwest-1.amazonaws.com.cn/protobuf-deserializer
  newTag: af613034c8f0cc8217e0ebaad5005fdd5abab0f0
resources:
- ../base
